.TH "scrcpy" "1"
.SH NAME
scrcpy \- Display and control your Android device


.SH SYNOPSIS
.B scrcpy
.RI [ options ]


.SH DESCRIPTION
.B scrcpy
provides display and control of Android devices connected on USB (or over TCP/IP). It does not require any root access.


.SH OPTIONS

.TP
.B \-\-always\-on\-top
Make scrcpy window always on top (above other windows).

.TP
.BI "\-\-angle " degrees
Rotate the video content by a custom angle, in degrees (clockwise).

.TP
.BI "\-\-audio\-bit\-rate " value
Encode the audio at the given bit rate, expressed in bits/s. Unit suffixes are supported: '\fBK\fR' (x1000) and '\fBM\fR' (x1000000).

Default is 128K (128000).

.TP
.BI "\-\-audio\-buffer " ms
Configure the audio buffering delay (in milliseconds).

Lower values decrease the latency, but increase the likelihood of buffer underrun (causing audio glitches).

Default is 50.

.TP
.BI "\-\-audio\-codec " name
Select an audio codec (opus, aac, flac or raw).

Default is opus.

.TP
.BI "\-\-audio\-codec\-options " key\fR[:\fItype\fR]=\fIvalue\fR[,...]
Set a list of comma-separated key:type=value options for the device audio encoder.

The possible values for 'type' are 'int' (default), 'long', 'float' and 'string'.

The list of possible codec options is available in the Android documentation:

<https://d.android.com/reference/android/media/MediaFormat>

.TP
.B \-\-audio\-dup
Duplicate audio (capture and keep playing on the device).

This feature is only available with --audio-source=playback.

.TP
.BI "\-\-audio\-encoder " name
Use a specific MediaCodec audio encoder (depending on the codec provided by \fB\-\-audio\-codec\fR).

The available encoders can be listed by \fB\-\-list\-encoders\fR.

.TP
.BI "\-\-audio\-source " source
Select the audio source. Possible values are:

 - "output": forwards the whole audio output, and disables playback on the device.
 - "playback": captures the audio playback (Android apps can opt-out, so the whole output is not necessarily captured).
 - "mic": captures the microphone.
 - "mic-unprocessed": captures the microphone unprocessed (raw) sound.
 - "mic-camcorder": captures the microphone tuned for video recording, with the same orientation as the camera if available.
 - "mic-voice-recognition": captures the microphone tuned for voice recognition.
 - "mic-voice-communication": captures the microphone tuned for voice communications (it will for instance take advantage of echo cancellation or automatic gain control if available).
 - "voice-call": captures voice call.
 - "voice-call-uplink": captures voice call uplink only.
 - "voice-call-downlink": captures voice call downlink only.
 - "voice-performance": captures audio meant to be processed for live performance (karaoke), includes both the microphone and the device playback.

Default is output.

.TP
.BI "\-\-audio\-output\-buffer " ms
Configure the size of the SDL audio output buffer (in milliseconds).

If you get "robotic" audio playback, you should test with a higher value (10). Do not change this setting otherwise.

Default is 5.

.TP
.BI "\-b, \-\-video\-bit\-rate " value
Encode the video at the given bit rate, expressed in bits/s. Unit suffixes are supported: '\fBK\fR' (x1000) and '\fBM\fR' (x1000000).

Default is 8M (8000000).

.TP
.BI "\-\-camera\-ar " ar
Select the camera size by its aspect ratio (+/- 10%).

Possible values are "sensor" (use the camera sensor aspect ratio), "\fInum\fR:\fIden\fR" (e.g. "4:3") and "\fIvalue\fR" (e.g. "1.6").

.TP
.BI "\-\-camera\-facing " facing
Select the device camera by its facing direction.

Possible values are "front", "back" and "external".

.TP
.BI "\-\-camera\-fps " fps
Specify the camera capture frame rate.

If not specified, Android's default frame rate (30 fps) is used.

.TP
.B \-\-camera\-high\-speed
Enable high-speed camera capture mode.

This mode is restricted to specific resolutions and frame rates, listed by \fB\-\-list\-camera\-sizes\fR.

.TP
.BI "\-\-camera\-id " id
Specify the device camera id to mirror.

The available camera ids can be listed by \fB\-\-list\-cameras\fR.

.TP
.BI "\-\-camera\-size " width\fRx\fIheight
Specify an explicit camera capture size.

.TP
.BI "\-\-capture\-orientation " value
Possible values are 0, 90, 180, 270, flip0, flip90, flip180 and flip270, possibly prefixed by '@'.

The number represents the clockwise rotation in degrees; the "flip" keyword applies a horizontal flip before the rotation.

If a leading '@' is passed (@90) for display capture, then the rotation is locked, and is relative to the natural device orientation.

If '@' is passed alone, then the rotation is locked to the initial device orientation.

Default is 0.

.TP
.BI "\-\-crop " width\fR:\fIheight\fR:\fIx\fR:\fIy
Crop the device screen on the server.

The values are expressed in the device natural orientation (typically, portrait for a phone, landscape for a tablet).

.TP
.B \-d, \-\-select\-usb
Use USB device (if there is exactly one, like adb -d).

Also see \fB\-e\fR (\fB\-\-select\-tcpip\fR).

.TP
.BI "\-\-disable\-screensaver"
Disable screensaver while scrcpy is running.

.TP
.BI "\-\-display\-id " id
Specify the device display id to mirror.

The available display ids can be listed by \fB\-\-list\-displays\fR.

Default is 0.

.TP
.BI "\-\-display\-ime\-policy " value
Set the policy for selecting where the IME should be displayed.

Possible values are "local", "fallback" and "hide":

 - "local" means that the IME should appear on the local display.
 - "fallback" means that the IME should appear on a fallback display (the default display).
 - "hide" means that the IME should be hidden.

By default, the IME policy is left unchanged.


.TP
.BI "\-\-display\-orientation " value
Set the initial display orientation.

Possible values are 0, 90, 180, 270, flip0, flip90, flip180 and flip270. The number represents the clockwise rotation in degrees; the "flip" keyword applies a horizontal flip before the rotation.

Default is 0.

.TP
.B \-e, \-\-select\-tcpip
Use TCP/IP device (if there is exactly one, like adb -e).

Also see \fB\-d\fR (\fB\-\-select\-usb\fR).

.TP
.B \-f, \-\-fullscreen
Start in fullscreen.

.TP
.B \-\-force\-adb\-forward
Do not attempt to use "adb reverse" to connect to the device.

.TP
.B \-G
Same as \fB\-\-gamepad=uhid\fR, or \fB\-\-keyboard=aoa\fR if \fB\-\-otg\fR is set.

.TP
.BI "\-\-gamepad " mode
Select how to send gamepad inputs to the device.

Possible values are "disabled", "uhid" and "aoa":

 - "disabled" does not send gamepad inputs to the device.
 - "uhid" simulates physical HID gamepads using the Linux HID kernel module on the device.
 - "aoa" simulates physical HID gamepads using the AOAv2 protocol. It may only work over USB.

Also see \fB\-\-keyboard\f and R\fB\-\-mouse\fR.
.TP
.B \-h, \-\-help
Print this help.

.TP
.B \-K
Same as \fB\-\-keyboard=uhid\fR, or \fB\-\-keyboard=aoa\fR if \fB\-\-otg\fR is set.

.TP
.BI "\-\-keyboard " mode
Select how to send keyboard inputs to the device.

Possible values are "disabled", "sdk", "uhid" and "aoa":

 - "disabled" does not send keyboard inputs to the device.
 - "sdk" uses the Android system API to deliver keyboard events to applications.
 - "uhid" simulates a physical HID keyboard using the Linux HID kernel module on the device.
 - "aoa" simulates a physical HID keyboard using the AOAv2 protocol. It may only work over USB.

For "uhid" and "aoa", the keyboard layout must be configured (once and for all) on the device, via Settings -> System -> Languages and input -> Physical keyboard. This settings page can be started directly using the shortcut MOD+k (except in OTG mode), or by executing:

    adb shell am start -a android.settings.HARD_KEYBOARD_SETTINGS

This option is only available when the HID keyboard is enabled (or a physical keyboard is connected).

Also see \fB\-\-mouse\fR and \fB\-\-gamepad\fR.

.TP
.B \-\-kill\-adb\-on\-close
Kill adb when scrcpy terminates.

.TP
.B \-\-legacy\-paste
Inject computer clipboard text as a sequence of key events on Ctrl+v (like MOD+Shift+v).

This is a workaround for some devices not behaving as expected when setting the device clipboard programmatically.

.TP
.B \-\-list\-apps
List Android apps installed on the device.

.TP
.B \-\-list\-camera\-sizes
List the valid camera capture sizes.

.TP
.B \-\-list\-cameras
List cameras available on the device.

.TP
.B \-\-list\-encoders
List video and audio encoders available on the device.

.TP
.B \-\-list\-displays
List displays available on the device.

.TP
.BI "\-m, \-\-max\-size " value
Limit both the width and height of the video to \fIvalue\fR. The other dimension is computed so that the device aspect\-ratio is preserved.

Default is 0 (unlimited).

.TP
.B \-M
Same as \fB\-\-mouse=uhid\fR, or \fB\-\-mouse=aoa\fR if \fB\-\-otg\fR is set.

.TP
.BI "\-\-max\-fps " value
Limit the framerate of screen capture (officially supported since Android 10, but may work on earlier versions).

.TP
.BI "\-\-mouse " mode
Select how to send mouse inputs to the device.

Possible values are "disabled", "sdk", "uhid" and "aoa":

 - "disabled" does not send mouse inputs to the device.
 - "sdk" uses the Android system API to deliver mouse events to applications.
 - "uhid" simulates a physical HID mouse using the Linux HID kernel module on the device.
 - "aoa" simulates a physical mouse using the AOAv2 protocol. It may only work over USB.

In "uhid" and "aoa" modes, the computer mouse is captured to control the device directly (relative mouse mode).

LAlt, LSuper or RSuper toggle the capture mode, to give control of the mouse back to the computer.

Also see \fB\-\-keyboard\fR and \fB\-\-gamepad\fR.

.TP
.BI "\-\-mouse\-bind " xxxx[:xxxx]
Configure bindings of secondary clicks.

The argument must be one or two sequences (separated by ':') of exactly 4 characters, one for each secondary click (in order: right click, middle click, 4th click, 5th click).

The first sequence defines the primary bindings, used when a mouse button is pressed alone. The second sequence defines the secondary bindings, used when a mouse button is pressed while the Shift key is held.

If the second sequence of bindings is omitted, then it is the same as the first one.

Each character must be one of the following:

 - '+': forward the click to the device
 - '-': ignore the click
 - 'b': trigger shortcut BACK (or turn screen on if off)
 - 'h': trigger shortcut HOME
 - 's': trigger shortcut APP_SWITCH
 - 'n': trigger shortcut "expand notification panel"

Default is 'bhsn:++++' for SDK mouse, and '++++:bhsn' for AOA and UHID.


.TP
.B \-n, \-\-no\-control
Disable device control (mirror the device in read\-only).

.TP
.B \-N, \-\-no\-playback
Disable video and audio playback on the computer (equivalent to \fB\-\-no\-video\-playback \-\-no\-audio\-playback\fR).

.TP
\fB\-\-new\-display\fR[=[\fIwidth\fRx\fIheight\fR][/\fIdpi\fR]]
Create a new display with the specified resolution and density. If not provided, they default to the main display dimensions and DPI.

Examples:

    \-\-new\-display=1920x1080
    \-\-new\-display=1920x1080/420
    \-\-new\-display         # main display size and density
    \-\-new\-display=/240    # main display size and 240 dpi

.TP
.B \-\-no\-audio
Disable audio forwarding.

.TP
.B \-\-no\-audio\-playback
Disable audio playback on the computer.

.TP
.B \-\-no\-cleanup
By default, scrcpy removes the server binary from the device and restores the device state (show touches, stay awake and power mode) on exit.

This option disables this cleanup.

.TP
.B \-\-no\-clipboard\-autosync
By default, scrcpy automatically synchronizes the computer clipboard to the device clipboard before injecting Ctrl+v, and the device clipboard to the computer clipboard whenever it changes.

This option disables this automatic synchronization.

.TP
.B \-\-no\-downsize\-on\-error
By default, on MediaCodec error, scrcpy automatically tries again with a lower definition.

This option disables this behavior.

.TP
.B \-\-no\-key\-repeat
Do not forward repeated key events when a key is held down.

.TP
.B \-\-no\-mipmaps
If the renderer is OpenGL 3.0+ or OpenGL ES 2.0+, then mipmaps are automatically generated to improve downscaling quality. This option disables the generation of mipmaps.

.TP
.B \-\-no\-mouse\-hover
Do not forward mouse hover (mouse motion without any clicks) events.

.TP
.B \-\-no\-power\-on
Do not power on the device on start.

.TP
.B \-\-no\-vd\-destroy\-content
Disable virtual display "destroy content on removal" flag.

With this option, when the virtual display is closed, the running apps are moved to the main display rather than being destroyed.

.TP
.B \-\-no\-vd\-system\-decorations
Disable virtual display system decorations flag.

.TP
.B \-\-no\-video
Disable video forwarding.

.TP
.B \-\-no\-video\-playback
Disable video playback on the computer.

.TP
.B \-\-no\-window
Disable scrcpy window. Implies --no-video-playback.

.TP
.BI "\-\-orientation " value
Same as --display-orientation=value --record-orientation=value.

.TP
.B \-\-otg
Run in OTG mode: simulate physical keyboard and mouse, as if the computer keyboard and mouse were plugged directly to the device via an OTG cable.

In this mode, adb (USB debugging) is not necessary, and mirroring is disabled.

LAlt, LSuper or RSuper toggle the mouse capture mode, to give control of the mouse back to the computer.

If any of \fB\-\-hid\-keyboard\fR or \fB\-\-hid\-mouse\fR is set, only enable keyboard or mouse respectively, otherwise enable both.

It may only work over USB.

See \fB\-\-keyboard\fR, \fB\-\-mouse\fR and \fB\-\-gamepad\fR.

.TP
.BI "\-p, \-\-port " port\fR[:\fIport\fR]
Set the TCP port (range) used by the client to listen.

Default is 27183:27199.

.TP
\fB\-\-pause\-on\-exit\fR[=\fImode\fR]
Configure pause on exit. Possible values are "true" (always pause on exit), "false" (never pause on exit) and "if-error" (pause only if an error occurred).

This is useful to prevent the terminal window from automatically closing, so that error messages can be read.

Default is "false".

Passing the option without argument is equivalent to passing "true".

.TP
.B \-\-power\-off\-on\-close
Turn the device screen off when closing scrcpy.

.TP
.B \-\-prefer\-text
Inject alpha characters and space as text events instead of key events.

This avoids issues when combining multiple keys to enter special characters,
but breaks the expected behavior of alpha keys in games (typically WASD).

.TP
.B "\-\-print\-fps
Start FPS counter, to print framerate logs to the console. It can be started or stopped at any time with MOD+i.

.TP
.BI "\-\-push\-target " path
Set the target directory for pushing files to the device by drag & drop. It is passed as\-is to "adb push".

Default is "/sdcard/Download/".

.TP
.BI "\-r, \-\-record " file
Record screen to
.IR file .

The format is determined by the
.B \-\-record\-format
option if set, or by the file extension.

.TP
.B \-\-raw\-key\-events
Inject key events for all input keys, and ignore text events.

.TP
.BI "\-\-record\-format " format
Force recording format (mp4, mkv, m4a, mka, opus, aac, flac or wav).

.TP
.BI "\-\-record\-orientation " value
Set the record orientation.

Possible values are 0, 90, 180 and 270. The number represents the clockwise rotation in degrees.

Default is 0.

.TP
.BI "\-\-render\-driver " name
Request SDL to use the given render driver (this is just a hint).

Supported names are currently "direct3d", "opengl", "opengles2", "opengles", "metal" and "software".

<https://wiki.libsdl.org/SDL_HINT_RENDER_DRIVER>

.TP
.B \-\-require\-audio
By default, scrcpy mirrors only the video if audio capture fails on the device. This option makes scrcpy fail if audio is enabled but does not work.

.TP
.BI "\-s, \-\-serial " number
The device serial number. Mandatory only if several devices are connected to adb.

.TP
.B \-S, \-\-turn\-screen\-off
Turn the device screen off immediately.

.TP
.B "\-\-screen\-off\-timeout " seconds
Set the screen off timeout while scrcpy is running (restore the initial value on exit).

.TP
.BI "\-\-shortcut\-mod " key\fR[+...]][,...]
Specify the modifiers to use for scrcpy shortcuts. Possible keys are "lctrl", "rctrl", "lalt", "ralt", "lsuper" and "rsuper".

Several shortcut modifiers can be specified, separated by ','.

For example, to use either LCtrl or LSuper for scrcpy shortcuts, pass "lctrl,lsuper".

Default is "lalt,lsuper" (left-Alt or left-Super).

.TP
.BI "\-\-start\-app " name
Start an Android app, by its exact package name.

Add a '?' prefix to select an app whose name starts with the given name, case-insensitive (retrieving app names on the device may take some time):

    scrcpy --start-app=?firefox

Add a '+' prefix to force-stop before starting the app:

    scrcpy --new-display --start-app=+org.mozilla.firefox

Both prefixes can be used, in that order:

    scrcpy --start-app=+?firefox

.TP
.B \-t, \-\-show\-touches
Enable "show touches" on start, restore the initial value on exit.

It only shows physical touches (not clicks from scrcpy).

.TP
.BI "\-\-tcpip\fR[=[+]\fIip\fR[:\fIport\fR]]
Configure and connect the device over TCP/IP.

If a destination address is provided, then scrcpy connects to this address before starting. The device must listen on the given TCP port (default is 5555).

If no destination address is provided, then scrcpy attempts to find the IP address and adb port of the current device (typically connected over USB), enables TCP/IP mode if necessary, then connects to this address before starting.

Prefix the address with a '+' to force a reconnection.

.TP
.BI "\-\-time\-limit " seconds
Set the maximum mirroring time, in seconds.

.TP
.BI "\-\-tunnel\-host " ip
Set the IP address of the adb tunnel to reach the scrcpy server. This option automatically enables \fB\-\-force\-adb\-forward\fR.

Default is localhost.

.TP
.BI "\-\-tunnel\-port " port
Set the TCP port of the adb tunnel to reach the scrcpy server. This option automatically enables \fB\-\-force\-adb\-forward\fR.

Default is 0 (not forced): the local port used for establishing the tunnel will be used.

.TP
.B \-v, \-\-version
Print the version of scrcpy.

.TP
.BI "\-V, \-\-verbosity " value
Set the log level ("verbose", "debug", "info", "warn" or "error").

Default is "info" for release builds, "debug" for debug builds.

.TP
.BI "\-\-v4l2-sink " /dev/videoN
Output to v4l2loopback device.

.TP
.BI "\-\-v4l2-buffer " ms
Add a buffering delay (in milliseconds) before pushing frames. This increases latency to compensate for jitter.

This option is similar to \fB\-\-video\-buffer\fR, but specific to V4L2 sink.

Default is 0 (no buffering).

.TP
.BI "\-\-video\-buffer " ms
Add a buffering delay (in milliseconds) before displaying video frames.

This increases latency to compensate for jitter.

Default is 0 (no buffering).

.TP
.BI "\-\-video\-codec " name
Select a video codec (h264, h265 or av1).

Default is h264.

.TP
.BI "\-\-video\-codec\-options " key\fR[:\fItype\fR]=\fIvalue\fR[,...]
Set a list of comma-separated key:type=value options for the device video encoder.

The possible values for 'type' are 'int' (default), 'long', 'float' and 'string'.

The list of possible codec options is available in the Android documentation:

<https://d.android.com/reference/android/media/MediaFormat>

.TP
.BI "\-\-video\-encoder " name
Use a specific MediaCodec video encoder (depending on the codec provided by \fB\-\-video\-codec\fR).

The available encoders can be listed by \fB\-\-list\-encoders\fR.

.TP
.BI "\-\-video\-source " source
Select the video source (display or camera).

Camera mirroring requires Android 12+.

Default is display.

.TP
.B \-w, \-\-stay-awake
Keep the device on while scrcpy is running, when the device is plugged in.

.TP
.B \-\-window\-borderless
Disable window decorations (display borderless window).

.TP
.BI "\-\-window\-title " text
Set a custom window title.

.TP
.BI "\-\-window\-x " value
Set the initial window horizontal position.

Default is "auto".

.TP
.BI "\-\-window\-y " value
Set the initial window vertical position.

Default is "auto".

.TP
.BI "\-\-window\-width " value
Set the initial window width.

Default is 0 (automatic).

.TP
.BI "\-\-window\-height " value
Set the initial window height.

Default is 0 (automatic).

.SH EXIT STATUS
.B scrcpy
will exit with code 0 on normal program termination. If an initial
connection cannot be established, the exit code 1 will be returned. If the
device disconnects while a session is active, exit code 2 will be returned.

.SH SHORTCUTS

In the following list, MOD is the shortcut modifier. By default, it's (left)
Alt or (left) Super, but it can be configured by \fB\-\-shortcut\-mod\fR (see above).

.TP
.B MOD+f
Switch fullscreen mode

.TP
.B MOD+Left
Rotate display left

.TP
.B MOD+Right
Rotate display right

.TP
.B MOD+Shift+Left, MOD+Shift+Right
Flip display horizontally

.TP
.B MOD+Shift+Up, MOD+Shift+Down
Flip display vertically

.TP
.B MOD+z
Pause or re-pause display

.TP
.B MOD+Shift+z
Unpause display

.TP
.B MOD+Shift+r
Reset video capture/encoding

.TP
.B MOD+g
Resize window to 1:1 (pixel\-perfect)

.TP
.B MOD+w, Double\-click on black borders
Resize window to remove black borders

.TP
.B MOD+h, Home, Middle\-click
Click on HOME

.TP
.B MOD+b, MOD+Backspace, Right\-click (when screen is on)
Click on BACK

.TP
.B MOD+s
Click on APP_SWITCH

.TP
.B MOD+m
Click on MENU

.TP
.B MOD+Up
Click on VOLUME_UP

.TP
.B MOD+Down
Click on VOLUME_DOWN

.TP
.B MOD+p
Click on POWER (turn screen on/off)

.TP
.B Right\-click (when screen is off)
Turn screen on

.TP
.B MOD+o
Turn device screen off (keep mirroring)

.TP
.B MOD+Shift+o
Turn device screen on

.TP
.B MOD+r
Rotate device screen

.TP
.B MOD+n
Expand notification panel

.TP
.B MOD+Shift+n
Collapse notification panel

.TP
.B Mod+c
Copy to clipboard (inject COPY keycode, Android >= 7 only)

.TP
.B Mod+x
Cut to clipboard (inject CUT keycode, Android >= 7 only)

.TP
.B MOD+v
Copy computer clipboard to device, then paste (inject PASTE keycode, Android >= 7 only)

.TP
.B MOD+Shift+v
Inject computer clipboard text as a sequence of key events

.TP
.B MOD+k
Open keyboard settings on the device (for HID keyboard only)

.TP
.B MOD+i
Enable/disable FPS counter (print frames/second in logs)

.TP
.B Ctrl+click-and-move
Pinch-to-zoom and rotate from the center of the screen

.TP
.B Shift+click-and-move
Tilt vertically (slide with 2 fingers)

.TP
.B Ctrl+Shift+click-and-move
Tilt horizontally (slide with 2 fingers)

.TP
.B Drag & drop APK file
Install APK from computer

.TP
.B Drag & drop non-APK file
Push file to device (see \fB\-\-push\-target\fR)


.SH Environment variables

.TP
.B ADB
Path to adb.

.TP
.B ANDROID_SERIAL
Device serial to use if no selector (\fB-s\fR, \fB-d\fR, \fB-e\fR or \fB\-\-tcpip=\fIaddr\fR) is specified.

.TP
.B SCRCPY_ICON_PATH
Path to the program icon.

.TP
.B SCRCPY_SERVER_PATH
Path to the server binary.


.SH AUTHORS
.B scrcpy
is written by Romain Vimont.

This manual page was written by
.MT <EMAIL>
Yangfl
.ME
for the Debian Project (and may be used by others).


.SH "REPORTING BUGS"
Report bugs to <https://github.com/Genymobile/scrcpy/issues>.

.SH COPYRIGHT
Copyright \(co 2018 Genymobile <https://www.genymobile.com>

Copyright \(co 2018\-2025 Romain Vimont <<EMAIL>>

Licensed under the Apache License, Version 2.0.

.SH WWW
<https://github.com/Genymobile/scrcpy>
