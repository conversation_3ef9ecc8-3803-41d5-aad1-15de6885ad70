apply plugin: 'com.android.application'

android {
    namespace 'com.genymobile.scrcpy'
    compileSdk 35
    defaultConfig {
        applicationId "com.genymobile.scrcpy"
        minSdkVersion 21
        targetSdkVersion 35
        versionCode 30301
        versionName "3.3.1"
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    buildFeatures {
        buildConfig true
        aidl true
    }
}

dependencies {
    testImplementation 'junit:junit:4.13.2'
}

apply from: "$project.rootDir/config/android-checkstyle.gradle"
