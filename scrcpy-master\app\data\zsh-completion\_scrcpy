#compdef scrcpy scrcpy.exe
#
# name: scrcpy
# auth: hltdev [<EMAIL>]
# desc: completion file for scrcpy (all OSes)
#

local arguments

arguments=(
    '--always-on-top[Make scrcpy window always on top \(above other windows\)]'
    '--angle=[Rotate the video content by a custom angle, in degrees]'
    '--audio-bit-rate=[Encode the audio at the given bit-rate]'
    '--audio-buffer=[Configure the audio buffering delay \(in milliseconds\)]'
    '--audio-codec=[Select the audio codec]:codec:(opus aac flac raw)'
    '--audio-codec-options=[Set a list of comma-separated key\:type=value options for the device audio encoder]'
    '--audio-dup=[Duplicate audio]'
    '--audio-encoder=[Use a specific MediaCodec audio encoder]'
    '--audio-source=[Select the audio source]:source:(output playback mic mic-unprocessed mic-camcorder mic-voice-recognition mic-voice-communication voice-call voice-call-uplink voice-call-downlink voice-performance)'
    '--audio-output-buffer=[Configure the size of the SDL audio output buffer (in milliseconds)]'
    {-b,--video-bit-rate=}'[Encode the video at the given bit-rate]'
    '--camera-ar=[Select the camera size by its aspect ratio]'
    '--camera-high-speed=[Enable high-speed camera capture mode]'
    '--camera-id=[Specify the camera id to mirror]'
    '--camera-facing=[Select the device camera by its facing direction]:facing:(front back external)'
    '--camera-fps=[Specify the camera capture frame rate]'
    '--camera-size=[Specify an explicit camera capture size]'
    '--capture-orientation=[Set the capture video orientation]:orientation:(0 90 180 270 flip0 flip90 flip180 flip270 @0 @90 @180 @270 @flip0 @flip90 @flip180 @flip270)'
    '--crop=[\[width\:height\:x\:y\] Crop the device screen on the server]'
    {-d,--select-usb}'[Use USB device]'
    '--disable-screensaver[Disable screensaver while scrcpy is running]'
    '--display-id=[Specify the display id to mirror]'
    '--display-ime-policy[Set the policy for selecting where the IME should be displayed]'
    '--display-orientation=[Set the initial display orientation]:orientation values:(0 90 180 270 flip0 flip90 flip180 flip270)'
    {-e,--select-tcpip}'[Use TCP/IP device]'
    {-f,--fullscreen}'[Start in fullscreen]'
    '--force-adb-forward[Do not attempt to use \"adb reverse\" to connect to the device]'
    '-G[Use UHID/AOA gamepad \(same as --gamepad=uhid or --gamepad=aoa, depending on OTG mode\)]'
    '--gamepad=[Set the gamepad input mode]:mode:(disabled uhid aoa)'
    {-h,--help}'[Print the help]'
    '-K[Use UHID/AOA keyboard \(same as --keyboard=uhid or --keyboard=aoa, depending on OTG mode\)]'
    '--keyboard=[Set the keyboard input mode]:mode:(disabled sdk uhid aoa)'
    '--kill-adb-on-close[Kill adb when scrcpy terminates]'
    '--legacy-paste[Inject computer clipboard text as a sequence of key events on Ctrl+v]'
    '--list-apps[List Android apps installed on the device]'
    '--list-camera-sizes[List the valid camera capture sizes]'
    '--list-cameras[List cameras available on the device]'
    '--list-displays[List displays available on the device]'
    '--list-encoders[List video and audio encoders available on the device]'
    {-m,--max-size=}'[Limit both the width and height of the video to value]'
    '-M[Use UHID/AOA mouse \(same as --mouse=uhid or --mouse=aoa, depending on OTG mode\)]'
    '--max-fps=[Limit the frame rate of screen capture]'
    '--mouse=[Set the mouse input mode]:mode:(disabled sdk uhid aoa)'
    '--mouse-bind=[Configure bindings of secondary clicks]'
    {-n,--no-control}'[Disable device control \(mirror the device in read only\)]'
    {-N,--no-playback}'[Disable video and audio playback]'
    '--new-display=[Create a new display]'
    '--no-audio[Disable audio forwarding]'
    '--no-audio-playback[Disable audio playback]'
    '--no-cleanup[Disable device cleanup actions on exit]'
    '--no-clipboard-autosync[Disable automatic clipboard synchronization]'
    '--no-downsize-on-error[Disable lowering definition on MediaCodec error]'
    '--no-key-repeat[Do not forward repeated key events when a key is held down]'
    '--no-mipmaps[Disable the generation of mipmaps]'
    '--no-mouse-hover[Do not forward mouse hover events]'
    '--no-power-on[Do not power on the device on start]'
    '--no-vd-destroy-content[Disable virtual display "destroy content on removal" flag]'
    '--no-vd-system-decorations[Disable virtual display system decorations flag]'
    '--no-video[Disable video forwarding]'
    '--no-video-playback[Disable video playback]'
    '--orientation=[Set the video orientation]:orientation values:(0 90 180 270 flip0 flip90 flip180 flip270)'
    '--otg[Run in OTG mode \(simulating physical keyboard and mouse\)]'
    {-p,--port=}'[\[port\[\:port\]\] Set the TCP port \(range\) used by the client to listen]'
    '--pause-on-exit=[Make scrcpy pause before exiting]:mode:(true false if-error)'
    '--power-off-on-close[Turn the device screen off when closing scrcpy]'
    '--prefer-text[Inject alpha characters and space as text events instead of key events]'
    '--print-fps[Start FPS counter, to print frame logs to the console]'
    '--push-target=[Set the target directory for pushing files to the device by drag and drop]'
    {-r,--record=}'[Record screen to file]:record file:_files'
    '--raw-key-events[Inject key events for all input keys, and ignore text events]'
    '--record-format=[Force recording format]:format:(mp4 mkv m4a mka opus aac flac wav)'
    '--record-orientation=[Set the record orientation]:orientation values:(0 90 180 270)'
    '--render-driver=[Request SDL to use the given render driver]:driver name:(direct3d opengl opengles2 opengles metal software)'
    '--require-audio=[Make scrcpy fail if audio is enabled but does not work]'
    {-s,--serial=}'[The device serial number \(mandatory for multiple devices only\)]:serial:($("${ADB-adb}" devices | awk '\''$2 == "device" {print $1}'\''))'
    {-S,--turn-screen-off}'[Turn the device screen off immediately]'
    '--screen-off-timeout=[Set the screen off timeout in seconds]'
    '--shortcut-mod=[\[key1,key2+key3,...\] Specify the modifiers to use for scrcpy shortcuts]:shortcut mod:(lctrl rctrl lalt ralt lsuper rsuper)'
    '--start-app=[Start an Android app]'
    {-t,--show-touches}'[Show physical touches]'
    '--tcpip[\(optional \[ip\:port\]\) Configure and connect the device over TCP/IP]'
    '--time-limit=[Set the maximum mirroring time, in seconds]'
    '--tunnel-host=[Set the IP address of the adb tunnel to reach the scrcpy server]'
    '--tunnel-port=[Set the TCP port of the adb tunnel to reach the scrcpy server]'
    '--v4l2-buffer=[Add a buffering delay \(in milliseconds\) before pushing frames]'
    '--v4l2-sink=[\[\/dev\/videoN\] Output to v4l2loopback device]'
    {-v,--version}'[Print the version of scrcpy]'
    {-V,--verbosity=}'[Set the log level]:verbosity:(verbose debug info warn error)'
    '--video-buffer=[Add a buffering delay \(in milliseconds\) before displaying video frames]'
    '--video-codec=[Select the video codec]:codec:(h264 h265 av1)'
    '--video-codec-options=[Set a list of comma-separated key\:type=value options for the device video encoder]'
    '--video-encoder=[Use a specific MediaCodec video encoder]'
    '--video-source=[Select the video source]:source:(display camera)'
    {-w,--stay-awake}'[Keep the device on while scrcpy is running, when the device is plugged in]'
    '--window-borderless[Disable window decorations \(display borderless window\)]'
    '--window-title=[Set a custom window title]'
    '--window-x=[Set the initial window horizontal position]'
    '--window-y=[Set the initial window vertical position]'
    '--window-width=[Set the initial window width]'
    '--window-height=[Set the initial window height]'
)

_arguments -s $arguments
