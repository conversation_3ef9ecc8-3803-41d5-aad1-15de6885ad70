---
name: Bug report
about: Create a report to help us improve
title: ''
labels: ''
assignees: ''

---

_Please read the [prerequisites] to run scrcpy._

[prerequisites]: https://github.com/Genymobile/scrcpy#prerequisites

_Also read the [FAQ] and check if your [issue][issues] already exists._

[FAQ]: https://github.com/Genymobile/scrcpy/blob/master/FAQ.md
[issues]: https://github.com/Genymobile/scrcpy/issues

## Environment

 - **OS:** [e.g. Debian, Windows, macOS...]
 - **Scrcpy version:** [e.g. 2.5]
 - **Installation method:** [e.g. manual build, apt, snap, brew, Windows release...]
 - **Device model:**
 - **Android version:** [e.g. 14]

## Describe the bug

A clear and concise description of what the bug is.

On errors, please provide the output of the console (and `adb logcat` if relevant).

```
Please paste terminal output in a code block.
```

Please do not post screenshots of your terminal, just post the content as text instead.
